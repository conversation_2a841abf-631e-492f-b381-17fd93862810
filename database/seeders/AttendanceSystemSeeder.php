<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Events;
use App\Models\Participants;
use App\Models\Groups;
use App\Models\EventParticipants;
use Carbon\Carbon;

class AttendanceSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Groups
        $groups = [
            ['name' => 'Group A', 'description' => 'Marketing Team', 'max_capacity' => 10, 'color_code' => '#3B82F6'],
            ['name' => 'Group B', 'description' => 'Development Team', 'max_capacity' => 8, 'color_code' => '#10B981'],
            ['name' => 'Group C', 'description' => 'Sales Team', 'max_capacity' => 12, 'color_code' => '#F59E0B'],
        ];

        foreach ($groups as $groupData) {
            Groups::create($groupData);
        }

        // Create Event
        $event = Events::create([
            'event_name' => 'Monthly Team Meeting',
            'event_date' => Carbon::now()->addDays(1),
            'description' => 'Monthly team meeting untuk review progress dan planning',
            'event_location' => 'Conference Room A',
            'created_by' => 'System',
            'barcode_value' => 'EVENT-MEETING-001',
            'is_active' => true,
        ]);

        // Create Participants
        $participants = [
            ['fullname' => 'John Doe', 'email' => '<EMAIL>', 'phone' => '081234567890', 'position' => 'Marketing Manager', 'company_name' => 'PT. Example Corp'],
            ['fullname' => 'Jane Smith', 'email' => '<EMAIL>', 'phone' => '081234567891', 'position' => 'Developer', 'company_name' => 'PT. Example Corp'],
            ['fullname' => 'Bob Wilson', 'email' => '<EMAIL>', 'phone' => '081234567892', 'position' => 'Sales Manager', 'company_name' => 'PT. Sample Ltd'],
            ['fullname' => 'Alice Brown', 'email' => '<EMAIL>', 'phone' => '081234567893', 'position' => 'UI/UX Designer', 'company_name' => 'PT. Sample Ltd'],
            ['fullname' => 'Charlie Davis', 'email' => '<EMAIL>', 'phone' => '081234567894', 'position' => 'Product Manager', 'company_name' => 'PT. Demo Inc'],
            ['fullname' => 'Diana Miller', 'email' => '<EMAIL>', 'phone' => '081234567895', 'position' => 'Sales Executive', 'company_name' => 'PT. Demo Inc'],
        ];

        $createdParticipants = [];
        foreach ($participants as $participantData) {
            $createdParticipants[] = Participants::create($participantData);
        }

        // Assign participants to event with groups
        $groupIds = Groups::pluck('id')->toArray();

        foreach ($createdParticipants as $index => $participant) {
            EventParticipants::create([
                'participant_id' => $participant->id,
                'event_id' => $event->id,
                'group_id' => $groupIds[$index % count($groupIds)], // Distribute evenly across groups
                'registration_status' => 'confirmed',
                'assigned_table' => 'Table ' . (($index % 3) + 1),
            ]);
        }

        $this->command->info('Sample data created successfully!');
        $this->command->info('Event Barcode: EVENT-MEETING-001');
        $this->command->info('Access scanner at: /attendance');
    }
}
