<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Memastikan field fullname ada dan konsisten
     */
    public function up(): void
    {
        Schema::table('participants', function (Blueprint $table) {
            // Pastikan field fullname ada (jika belum ada)
            if (!Schema::hasColumn('participants', 'fullname')) {
                $table->string('fullname')->after('id');
            }
            
            // Jika ada field full_name yang salah, hapus
            if (Schema::hasColumn('participants', 'full_name')) {
                // Copy data dari full_name ke fullname jika fullname kosong
                DB::statement('UPDATE participants SET fullname = full_name WHERE fullname IS NULL OR fullname = ""');
                
                // Hapus field full_name yang salah
                $table->dropColumn('full_name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Tidak perlu rollback karena ini adalah perbaikan konsistensi
    }
};
