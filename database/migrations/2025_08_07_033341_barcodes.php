<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // create table barcodes
        Schema::create('barcodes', function (Blueprint $table) {
            $table->id();
            $table->string('barcode_value');
            $table->foreignId('session_id');
            $table->string('usage_count');
            $table->boolean('is_used');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //drop table
        Schema::dropIfExists('barcodes');
    }
};
