<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //create table event participants
        Schema::create('event_participants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('participant_id');
            $table->foreignId('event_id');
            $table->foreignId('session_id');
            $table->boolean('registration_status')->default(false);
            $table->string ('assigned_table')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //drop table
        Schema::dropIfExists('event_participants');
    }
};
