<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //cretea table notifications
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            //event participant_id
            $table->string('event_participant_id');
            $table->string('event_id');
            $table->string('sesssion_id');
            $table->string('notification_type');
            $table->string('channel');
            $table->string('recipient');
            $table->string('subject');
            $table->string('message');
            $table->string('status');
            $table->string('sent_at');
            $table->string('error_message');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
