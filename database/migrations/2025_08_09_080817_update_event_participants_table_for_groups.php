<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('event_participants', function (Blueprint $table) {
            $table->dropColumn('session_id');
            $table->foreignId('group_id')->nullable()->after('event_id')->constrained('groups')->onDelete('set null');
            $table->string('registration_status')->default('pending')->change();
            $table->string('assigned_table')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('event_participants', function (Blueprint $table) {
            $table->dropForeign(['group_id']);
            $table->dropColumn('group_id');
            $table->foreignId('session_id')->after('event_id');
            $table->boolean('registration_status')->default(false)->change();
            $table->string('assigned_table')->default(false)->change();
        });
    }
};
