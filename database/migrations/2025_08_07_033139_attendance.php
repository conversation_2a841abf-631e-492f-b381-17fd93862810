<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //create table attendances
        Schema::create('attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_participant_id')->constrained('event_participants')->onDelete('cascade');
            $table->timestamp('check_in_time');
            $table->string('location')->nullable();
            $table->string('device_ip', 45)->nullable();
            $table->string('user_agent', 500)->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //drop table
        Schema::dropIfExists('attendances');
    }
};
