<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AttendanceWebController;

Route::get('/', function () {
    return redirect()->route('qrcode.demo');
});

// Routes untuk attendance web interface
Route::prefix('attendance')->group(function () {
    Route::get('/', [AttendanceWebController::class, 'index'])->name('attendance.scanner');
    Route::get('/select-participant', [AttendanceWebController::class, 'selectParticipant'])->name('attendance.select-participant');
    Route::get('/confirmation', [AttendanceWebController::class, 'confirmation'])->name('attendance.confirmation');
});

// Routes untuk QR Code
Route::prefix('qrcode')->group(function () {
    Route::get('/demo', [App\Http\Controllers\QRCodeController::class, 'demo'])->name('qrcode.demo');
    Route::get('/event/{event}', [App\Http\Controllers\QRCodeController::class, 'showEventQR'])->name('qrcode.event');
    Route::get('/generate/event/{event}', [App\Http\Controllers\QRCodeController::class, 'generateEventQR'])->name('qrcode.generate.event');
    Route::get('/generate', [App\Http\Controllers\QRCodeController::class, 'generateQR'])->name('qrcode.generate');
});
