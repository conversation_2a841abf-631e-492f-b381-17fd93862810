<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AttendanceController;

// Public API routes untuk attendance system
Route::prefix('attendance')->group(function () {
    Route::post('/scan-barcode', [AttendanceController::class, 'scanBarcode']);
    Route::post('/submit', [AttendanceController::class, 'submitAttendance']);
    Route::get('/group-info', [AttendanceController::class, 'getGroupInfo']);
});

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');
