<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code - {{ $event->event_name }}</title>
    <link rel="stylesheet" href="{{ asset('build/assets/app--YM2BVBO.css') }}">
    <script src="{{ asset('build/assets/app-C0G0cght.js') }}" defer></script>
       <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 min-h-screen flex items-center justify-center">
    <div class="max-w-lg mx-auto p-8">
        <!-- Event Info -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-bold text-white mb-2">{{ $event->event_name }}</h1>
            <div class="space-y-1 text-gray-300">
                <p>📅 {{ $event->event_date->format('d M Y, H:i') }}</p>
                <p>📍 {{ $event->event_location }}</p>
            </div>
        </div>

        <!-- QR Code -->
        <div class="bg-white rounded-2xl p-8 shadow-2xl text-center">
            <div class="mb-6">
                <img src="{{ route('qrcode.generate.event', $event->id) }}"
                     alt="QR Code for {{ $event->event_name }}"
                     class="w-80 h-80 mx-auto">
            </div>

            <div class="border-t pt-6">
                <p class="text-sm text-gray-600 mb-2">Barcode Value:</p>
                <code class="bg-gray-100 px-3 py-2 rounded text-lg font-mono">{{ $event->barcode_value }}</code>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-8 text-center">
            <p class="text-gray-300 mb-4">Scan QR code ini untuk check-in ke event</p>
            <div class="space-x-4">
                <a href="{{ route('qrcode.demo') }}"
                   class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg font-medium">
                    ← Kembali
                </a>
                <a href="{{ route('attendance.scanner') }}"
                   class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium">
                    📷 Buka Scanner
                </a>
            </div>
        </div>
    </div>

    <!-- Auto-refresh untuk demo -->
    <script>
        // Auto refresh setiap 30 detik untuk demo
        setTimeout(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
