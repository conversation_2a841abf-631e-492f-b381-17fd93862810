<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pilih Peserta - Attendance System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-center mb-6 text-gray-800">
                Pilih Peserta untuk Attendance
            </h1>
            
            <div class="mb-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded">
                <strong>Barcode Event:</strong> {{ $barcodeValue }}
            </div>
            
            <div id="loading" class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Memuat daftar peserta...</p>
            </div>
            
            <div id="participants-list" class="hidden">
                <div class="mb-4">
                    <input type="text" id="search" placeholder="Cari peserta..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div id="participants-container" class="space-y-2 max-h-96 overflow-y-auto">
                    <!-- Participants akan dimuat di sini -->
                </div>
            </div>
            
            <div id="error-message" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <!-- Error message akan ditampilkan di sini -->
            </div>
            
            <div class="mt-6 text-center">
                <a href="/attendance" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Kembali ke Scanner
                </a>
            </div>
        </div>
    </div>

    <script>
        let allParticipants = [];
        
        // Load participants saat halaman dimuat
        document.addEventListener('DOMContentLoaded', function() {
            loadParticipants();
        });
        
        async function loadParticipants() {
            try {
                const response = await axios.post('/api/attendance/scan-barcode', {
                    barcode_value: '{{ $barcodeValue }}'
                });
                
                if (response.data.success) {
                    allParticipants = response.data.data.participants;
                    displayParticipants(allParticipants);
                    
                    document.getElementById('loading').classList.add('hidden');
                    document.getElementById('participants-list').classList.remove('hidden');
                } else {
                    showError(response.data.message);
                }
            } catch (error) {
                showError('Terjadi kesalahan saat memuat data peserta');
                console.error('Error:', error);
            }
        }
        
        function displayParticipants(participants) {
            const container = document.getElementById('participants-container');
            container.innerHTML = '';
            
            if (participants.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500 py-4">Tidak ada peserta yang tersedia</p>';
                return;
            }
            
            participants.forEach(participant => {
                const participantDiv = document.createElement('div');
                participantDiv.className = 'border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer';
                participantDiv.onclick = () => selectParticipant(participant.id);
                
                participantDiv.innerHTML = `
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="font-semibold text-gray-800">${participant.participant.fullname}</h3>
                            <p class="text-sm text-gray-600">${participant.participant.company_name}</p>
                            <p class="text-xs text-blue-600">Grup: ${participant.group ? participant.group.name : 'Tidak ada grup'}</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-block px-2 py-1 text-xs rounded-full ${getStatusColor(participant.registration_status)}">
                                ${getStatusText(participant.registration_status)}
                            </span>
                        </div>
                    </div>
                `;
                
                container.appendChild(participantDiv);
            });
        }
        
        function getStatusColor(status) {
            switch(status) {
                case 'confirmed': return 'bg-green-100 text-green-800';
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'cancelled': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }
        
        function getStatusText(status) {
            switch(status) {
                case 'confirmed': return 'Dikonfirmasi';
                case 'pending': return 'Menunggu';
                case 'cancelled': return 'Dibatalkan';
                default: return status;
            }
        }
        
        async function selectParticipant(eventParticipantId) {
            try {
                const response = await axios.post('/api/attendance/submit', {
                    event_participant_id: eventParticipantId,
                    location: 'Scanner Web',
                    notes: 'Attendance via web scanner'
                });
                
                if (response.data.success) {
                    // Redirect ke halaman konfirmasi
                    window.location.href = `/attendance/confirmation?event_participant_id=${eventParticipantId}`;
                } else {
                    showError(response.data.message);
                }
            } catch (error) {
                showError('Terjadi kesalahan saat menyimpan attendance');
                console.error('Error:', error);
            }
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
            
            document.getElementById('loading').classList.add('hidden');
        }
        
        // Search functionality
        document.getElementById('search').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const filteredParticipants = allParticipants.filter(participant => 
                participant.participant.fullname.toLowerCase().includes(searchTerm) ||
                participant.participant.company_name.toLowerCase().includes(searchTerm)
            );
            displayParticipants(filteredParticipants);
        });
    </script>
</body>
</html>
