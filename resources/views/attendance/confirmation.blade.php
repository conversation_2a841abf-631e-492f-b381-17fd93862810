<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Konfirmasi Attendance - Attendance System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header Success -->
            <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-6">
                <div class="flex items-center">
                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <h1 class="text-xl font-bold">Attendance Berhasil Dicatat!</h1>
                </div>
            </div>

            <div id="loading" class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Memuat informasi attendance...</p>
            </div>

            <div id="content" class="hidden">
                <!-- Informasi Peserta -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-lg font-semibold mb-4 text-gray-800">Informasi Peserta</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-600">Nama Peserta</p>
                            <p class="font-semibold" id="participant-name">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Perusahaan</p>
                            <p class="font-semibold" id="participant-company">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Event</p>
                            <p class="font-semibold" id="event-name">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Waktu Check-in</p>
                            <p class="font-semibold" id="checkin-time">-</p>
                        </div>
                    </div>
                </div>

                <!-- Informasi Grup -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-lg font-semibold mb-4 text-gray-800">Informasi Grup</h2>
                    
                    <div class="mb-4">
                        <p class="text-sm text-gray-600">Nama Grup</p>
                        <p class="font-semibold text-lg" id="group-name">-</p>
                    </div>

                    <!-- Statistik Grup -->
                    <div class="grid grid-cols-3 gap-4 mb-6">
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <p class="text-2xl font-bold text-green-600" id="attended-count">0</p>
                            <p class="text-sm text-gray-600">Sudah Hadir</p>
                        </div>
                        <div class="text-center p-4 bg-yellow-50 rounded-lg">
                            <p class="text-2xl font-bold text-yellow-600" id="not-attended-count">0</p>
                            <p class="text-sm text-gray-600">Belum Hadir</p>
                        </div>
                        <div class="text-center p-4 bg-red-50 rounded-lg">
                            <p class="text-2xl font-bold text-red-600" id="absent-count">0</p>
                            <p class="text-sm text-gray-600">Tidak Hadir</p>
                        </div>
                    </div>

                    <!-- Daftar Anggota Grup -->
                    <div class="space-y-4">
                        <!-- Sudah Hadir -->
                        <div>
                            <h3 class="font-semibold text-green-700 mb-2 flex items-center">
                                <span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                                Anggota yang Sudah Hadir
                            </h3>
                            <div id="attended-members" class="space-y-2 ml-5">
                                <!-- Akan diisi dengan JavaScript -->
                            </div>
                        </div>

                        <!-- Belum Hadir -->
                        <div>
                            <h3 class="font-semibold text-yellow-700 mb-2 flex items-center">
                                <span class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                                Anggota yang Belum Hadir
                            </h3>
                            <div id="not-attended-members" class="space-y-2 ml-5">
                                <!-- Akan diisi dengan JavaScript -->
                            </div>
                        </div>

                        <!-- Tidak Hadir -->
                        <div>
                            <h3 class="font-semibold text-red-700 mb-2 flex items-center">
                                <span class="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                                Anggota yang Tidak Hadir
                            </h3>
                            <div id="absent-members" class="space-y-2 ml-5">
                                <!-- Akan diisi dengan JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="error-message" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <!-- Error message akan ditampilkan di sini -->
            </div>

            <!-- Action Buttons -->
            <div class="text-center space-x-4">
                <a href="/attendance" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded">
                    Scan Lagi
                </a>
                <button onclick="window.print()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded">
                    Cetak
                </button>
            </div>
        </div>
    </div>

    <script>
        // Load data saat halaman dimuat
        document.addEventListener('DOMContentLoaded', function() {
            loadAttendanceInfo();
        });

        async function loadAttendanceInfo() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const eventParticipantId = urlParams.get('event_participant_id');
                
                if (!eventParticipantId) {
                    showError('ID peserta tidak ditemukan');
                    return;
                }

                const response = await axios.get(`/api/attendance/group-info?event_participant_id=${eventParticipantId}`);
                
                if (response.data.success) {
                    displayAttendanceInfo(response.data.data);
                    document.getElementById('loading').classList.add('hidden');
                    document.getElementById('content').classList.remove('hidden');
                } else {
                    showError(response.data.message);
                }
            } catch (error) {
                showError('Terjadi kesalahan saat memuat informasi attendance');
                console.error('Error:', error);
            }
        }

        function displayAttendanceInfo(data) {
            // Informasi peserta
            document.getElementById('participant-name').textContent = data.participant.name;
            document.getElementById('participant-company').textContent = data.participant.company;
            document.getElementById('event-name').textContent = data.event.name;
            document.getElementById('checkin-time').textContent = new Date(data.attendance.check_in_time).toLocaleString('id-ID');

            // Informasi grup
            document.getElementById('group-name').textContent = data.group.name;

            // Statistik
            const attendedCount = data.group.participants.filter(p => p.attendance_status === 'attended').length;
            const notAttendedCount = data.group.participants.filter(p => p.attendance_status === 'not_attended').length;
            const absentCount = data.group.participants.filter(p => p.attendance_status === 'absent').length;

            document.getElementById('attended-count').textContent = attendedCount;
            document.getElementById('not-attended-count').textContent = notAttendedCount;
            document.getElementById('absent-count').textContent = absentCount;

            // Daftar anggota
            displayMembers('attended-members', data.group.participants.filter(p => p.attendance_status === 'attended'));
            displayMembers('not-attended-members', data.group.participants.filter(p => p.attendance_status === 'not_attended'));
            displayMembers('absent-members', data.group.participants.filter(p => p.attendance_status === 'absent'));
        }

        function displayMembers(containerId, members) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            if (members.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-sm">Tidak ada anggota</p>';
                return;
            }

            members.forEach(member => {
                const memberDiv = document.createElement('div');
                memberDiv.className = 'flex justify-between items-center p-3 bg-gray-50 rounded border';
                memberDiv.innerHTML = `
                    <div>
                        <p class="font-medium">${member.name}</p>
                        <p class="text-sm text-gray-600">${member.company}</p>
                    </div>
                    <div class="text-right">
                        ${member.check_in_time ? 
                            `<p class="text-xs text-gray-500">${new Date(member.check_in_time).toLocaleString('id-ID')}</p>` : 
                            ''
                        }
                    </div>
                `;
                container.appendChild(memberDiv);
            });
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
            
            document.getElementById('loading').classList.add('hidden');
        }
    </script>
</body>
</html>
