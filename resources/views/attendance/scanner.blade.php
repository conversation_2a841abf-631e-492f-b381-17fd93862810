<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scanner Barcode - Attendance System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-center mb-6 text-gray-800">
                Scanner Barcode Event
            </h1>
            
            <div id="reader" class="mb-4"></div>
            
            <div class="text-center">
                <p class="text-gray-600 mb-4">
                    Arahkan kamera ke barcode event untuk memulai proses attendance
                </p>
                
                <div id="result" class="hidden bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <strong>Barcode terdeteksi:</strong> <span id="barcode-value"></span>
                </div>
                
                <button id="start-scan" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Mulai Scan
                </button>
                
                <button id="stop-scan" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded hidden">
                    Stop Scan
                </button>
            </div>
        </div>
    </div>

    <script>
        let html5QrcodeScanner;
        
        function onScanSuccess(decodedText, decodedResult) {
            // Tampilkan hasil scan
            document.getElementById('barcode-value').textContent = decodedText;
            document.getElementById('result').classList.remove('hidden');
            
            // Stop scanner
            html5QrcodeScanner.clear();
            
            // Redirect ke halaman pilih participant
            setTimeout(() => {
                window.location.href = `/attendance/select-participant?barcode=${encodeURIComponent(decodedText)}`;
            }, 2000);
        }
        
        function onScanFailure(error) {
            // Handle scan failure, usually better to ignore and keep scanning
        }
        
        document.getElementById('start-scan').addEventListener('click', function() {
            html5QrcodeScanner = new Html5QrcodeScanner(
                "reader",
                { fps: 10, qrbox: {width: 250, height: 250} },
                false
            );
            html5QrcodeScanner.render(onScanSuccess, onScanFailure);
            
            this.classList.add('hidden');
            document.getElementById('stop-scan').classList.remove('hidden');
        });
        
        document.getElementById('stop-scan').addEventListener('click', function() {
            if (html5QrcodeScanner) {
                html5QrcodeScanner.clear();
            }
            
            this.classList.add('hidden');
            document.getElementById('start-scan').classList.remove('hidden');
            document.getElementById('result').classList.add('hidden');
        });
    </script>
</body>
</html>
