<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Attendance extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'attendances';

    protected $fillable = [
        'event_participant_id',
        'check_in_time',
        'location',
        'device_ip',
        'user_agent',
        'notes',
    ];

    protected $casts = [
        'check_in_time' => 'datetime',
    ];

    /**
     * Relasi ke event participant
     */
    public function eventParticipant()
    {
        return $this->belongsTo(EventParticipants::class, 'event_participant_id');
    }

    /**
     * Relasi ke participant melalui event_participant
     */
    public function participant()
    {
        return $this->hasOneThrough(
            Participants::class,
            EventParticipants::class,
            'id',
            'id',
            'event_participant_id',
            'participant_id'
        );
    }

    /**
     * Relasi ke event melalui event_participant
     */
    public function event()
    {
        return $this->hasOneThrough(
            Events::class,
            EventParticipants::class,
            'id',
            'id',
            'event_participant_id',
            'event_id'
        );
    }

    /**
     * Relasi ke group melalui event_participant
     */
    public function group()
    {
        return $this->hasOneThrough(
            Groups::class,
            EventParticipants::class,
            'id',
            'id',
            'event_participant_id',
            'group_id'
        );
    }

    /**
     * Scope untuk filter berdasarkan event
     */
    public function scopeForEvent($query, $eventId)
    {
        return $query->whereHas('eventParticipant', function($q) use ($eventId) {
            $q->where('event_id', $eventId);
        });
    }

    /**
     * Scope untuk filter berdasarkan tanggal
     */
    public function scopeForDate($query, $date)
    {
        return $query->whereDate('check_in_time', $date);
    }

    /**
     * Create attendance record
     */
    public static function createAttendance($eventParticipantId, $additionalData = [])
    {
        return self::create(array_merge([
            'event_participant_id' => $eventParticipantId,
            'check_in_time' => now(),
            'device_ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ], $additionalData));
    }
}
