<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Notification extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'notifications';
    protected $primaryKey = 'notification_id'; // or null, depending on your needs
    protected $keyType = 'string';
    public $incrementing = true;
    public $timestamps = true;

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];


    protected $fillable = [
        'event_participant_id',
        'participant_id',
        'event_id',
        'session_id',
        'notification_type',
        'channel',
        'recipient',
        'subject',
        'message',
        'status',
        'sent_at',
        'error_mesage'
    ];


    protected $casts = [
        'sent_at' => 'datetime(6)',
        'created_at' => 'datetime(6)',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(fn ($n) => $n->notification_id = (string) \Illuminate\Support\Str::uuid());
    }

    public function eventParticipant()
    {
        return $this->belongsTo(EventParticipants::class, 'event_participant_id');
    }
}
