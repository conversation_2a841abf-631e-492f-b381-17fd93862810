<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Events extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'events';
    protected $fillable = [
        'event_name',
        'event_date',
        'description',
        'event_location',
        'created_by',
        'barcode_value', // untuk identifikasi event via barcode
        'is_active',
    ];

    // Laravel 11 - Menggunakan casts method
    protected function casts(): array
    {
        return [
            'event_date' => 'datetime',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relasi ke event participants
     */
    public function eventParticipants()
    {
        return $this->hasMany(EventParticipants::class, 'event_id');
    }

    /**
     * Relasi ke participants melalui event_participants
     */
    public function participants()
    {
        return $this->belongsToMany(Participants::class, 'event_participants', 'event_id', 'participant_id')
                    ->withPivot('group_id', 'registration_status', 'assigned_table')
                    ->withTimestamps();
    }

    /**
     * <PERSON>lasi ke groups yang digunakan dalam event ini
     */
    public function groups()
    {
        return $this->belongsToMany(Groups::class, 'event_participants', 'event_id', 'group_id')
                    ->distinct();
    }

    /**
     * Relasi ke attendance melalui event_participants
     */
    public function attendances()
    {
        return $this->hasManyThrough(Attendance::class, EventParticipants::class, 'event_id', 'event_participant_id');
    }

    /**
     * Get participants yang belum attendance
     */
    public function getAvailableParticipantsAttribute()
    {
        return $this->eventParticipants()
                    ->whereDoesntHave('attendance')
                    ->with(['participant', 'group'])
                    ->get();
    }

    /**
     * Find event by barcode value
     */
    public static function findByBarcode($barcodeValue)
    {
        return static::where('barcode_value', $barcodeValue)
                    ->where('is_active', true)
                    ->first();
    }


}
