<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Groups extends Model
{
    use HasFactory;

    protected $table = 'groups';
    protected $fillable = [
        'name',
        'description',
        'max_capacity',
        'color_code', // untuk UI grouping
    ];

    // Laravel 11 - Menggunakan casts method
    protected function casts(): array
    {
        return [
            'max_capacity' => 'integer',
        ];
    }

    /**
     * Relasi ke event participants
     */
    public function eventParticipants()
    {
        return $this->hasMany(EventParticipants::class, 'group_id');
    }

    /**
     * Relasi ke participants melalui event_participants
     */
    public function participants()
    {
        return $this->belongsToMany(Participants::class, 'event_participants', 'group_id', 'participant_id')
                    ->withPivot('event_id', 'registration_status', 'assigned_table')
                    ->withTimestamps();
    }

    /**
     * Relasi ke events melalui event_participants
     */
    public function events()
    {
        return $this->belongsToMany(Events::class, 'event_participants', 'group_id', 'event_id')
                    ->distinct();
    }

    /**
     * Get participants untuk event tertentu
     */
    public function getParticipantsForEvent($eventId)
    {
        return $this->eventParticipants()
                    ->where('event_id', $eventId)
                    ->with('participant')
                    ->get()
                    ->pluck('participant');
    }

    /**
     * Get jumlah participants dalam group untuk event tertentu
     */
    public function getParticipantCountForEvent($eventId)
    {
        return $this->eventParticipants()
                    ->where('event_id', $eventId)
                    ->count();
    }
}
