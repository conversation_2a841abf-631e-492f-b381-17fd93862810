<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Barcodes extends Model
{
    use HasFactory;
    use SoftDeletes;


    protected $table = 'barcodes';
    protected $primaryKey = 'barcode_id';
    protected $metaTable = "barcodes_meta";
    public $incrementing = false;
    protected $keyType = 'string';
    protected $fillable = [
        'barcode_value', 
        'session_id', 
        'usage_count', 
        'is_active',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(fn ($barcode) => $barcode->barcode_id = (string) \Illuminate\Support\Str::uuid());
    }

    public function session()
    {
        return $this->belongsTo(Sessions::class);
    }
}
