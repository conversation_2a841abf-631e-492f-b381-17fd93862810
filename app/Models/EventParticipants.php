<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EventParticipants extends Model
{
    use HasFactory;

    protected $table = 'event_participants';

    protected $fillable = [
        'participant_id',
        'event_id',
        'group_id',
        'registration_status',
        'assigned_table',
    ];

    protected $casts = [
        'registration_status' => 'string',
    ];

    /**
     * Relasi ke participant
     */
    public function participant()
    {
        return $this->belongsTo(Participants::class, 'participant_id');
    }

    /**
     * Relasi ke event
     */
    public function event()
    {
        return $this->belongsTo(Events::class, 'event_id');
    }

    /**
     * Relasi ke group
     */
    public function group()
    {
        return $this->belongsTo(Groups::class, 'group_id');
    }

    /**
     * Relasi ke attendance
     */
    public function attendance()
    {
        return $this->hasOne(Attendance::class, 'event_participant_id');
    }

    /**
     * Scope untuk participants yang belum attendance
     */
    public function scopeNotAttended($query)
    {
        return $query->whereDoesntHave('attendance');
    }

    /**
     * Check apakah sudah attendance
     */
    public function hasAttended()
    {
        return $this->attendance()->exists();
    }

    /**
     * Check apakah sudah attendance
     */
    public function hasAttended()
    {
        return $this->attendance()->exists();
    }

    /**
     * Scope untuk filter berdasarkan event
     */
    public function scopeForEvent($query, $eventId)
    {
        return $query->where('event_id', $eventId);
    }

    /**
     * Scope untuk filter berdasarkan group
     */
    public function scopeForGroup($query, $groupId)
    {
        return $query->where('group_id', $groupId);
    }

    /**
     * Scope untuk yang belum attendance
     */
    public function scopeNotAttended($query)
    {
        return $query->whereDoesntHave('attendance');
    }

    /**
     * Get formatted info untuk display
     */
    public function getDisplayInfoAttribute()
    {
        return [
            'participant_name' => $this->participant->full_name,
            'group_name' => $this->group->name ?? 'No Group',
            'group_id' => $this->group_id,
            'has_attended' => $this->hasAttended(),
        ];
    }
}
