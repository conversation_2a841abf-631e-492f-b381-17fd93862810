<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Sessions extends Model
{

    protected $table = 'sessions';
    protected $metaTable = "sessions_meta";
    protected $fillable = [
        'session_name',
        'table_number',
        'start_time',
        'end_time',
    ];


     protected static function boot()
    {
        parent::boot();
        static::creating(fn ($session) => $session->session_id = (string) \Illuminate\Support\Str::uuid());
    }

    public function event()
    {
        return $this->belongsTo(Events::class);
    }
    public function participants()
    {
        return $this->belongsToMany(Participants::class, 'event_participants', 'session_id', 'participant_id');
    }
}
