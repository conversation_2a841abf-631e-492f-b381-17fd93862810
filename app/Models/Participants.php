<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Participants extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = "participants"; // fix typo dari $tabel ke $table
    protected $fillable = [
        'fullname',
        'email',
        'phone',
        'position',
        'company_name',
    ];

    // Laravel 11 - Menggunakan casts untuk attributes
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
        ];
    }

    /**
     * Relasi ke event participants
     */
    public function eventParticipants()
    {
        return $this->hasMany(EventParticipants::class, 'participant_id');
    }

    /**
     * Relasi ke events melalui event_participants
     */
    public function events()
    {
        return $this->belongsToMany(Events::class, 'event_participants', 'participant_id', 'event_id')
                    ->withPivot('group_id', 'registration_status', 'assigned_table')
                    ->withTimestamps();
    }

    /**
     * Accessor untuk fullname dengan company
     */
    public function getFullnameWithCompanyAttribute()
    {
        return $this->fullname . ' - ' . $this->company_name;
    }

    /**
     * Accessor untuk formatted name
     */
    public function getFormattedNameAttribute()
    {
        return $this->fullname . ' (' . $this->company_name . ')';
    }

    /**
     * Relasi ke attendance melalui event_participants
     */
    public function attendances()
    {
        return $this->hasManyThrough(Attendance::class, EventParticipants::class, 'participant_id', 'event_participant_id');
    }



    /**
     * Accessor untuk full_name (backward compatibility)
     */
    // public function getFullNameAttribute()
    // {
    //         // return $this->fullname;

    // }
        public function getFullNameAttribute()
    {
        return $this->attributes['fullname'] ?? null;
    }

    /**
     * Scope untuk search participants
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('fullname', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('company_name', 'like', "%{$search}%");
        });
    }
}
