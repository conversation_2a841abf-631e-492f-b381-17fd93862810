<?php

namespace App\Observers;

use App\Models\Events;
use Illuminate\Support\Str;

class EventsObserver
{
    /**
     * Handle the Events "creating" event.
     * Generate barcode otomatis saat event dibuat
     */
    public function creating(Events $event): void
    {
        if (empty($event->barcode_value)) {
            $event->barcode_value = $this->generateUniqueBarcode();
        }
    }

    /**
     * Generate barcode unik untuk event
     */
    private function generateUniqueBarcode(): string
    {
        do {
            // Format: EVENT-YYYY-MM-DD-RANDOM
            $date = now()->format('Y-m-d');
            $random = strtoupper(Str::random(6));
            $barcode = "EVENT-{$date}-{$random}";
        } while (Events::where('barcode_value', $barcode)->exists());

        return $barcode;
    }

    /**
     * Handle the Events "created" event.
     */
    public function created(Events $event): void
    {
        //
    }

    /**
     * Handle the Events "updated" event.
     */
    public function updated(Events $event): void
    {
        //
    }

    /**
     * Handle the Events "deleted" event.
     */
    public function deleted(Events $event): void
    {
        //
    }

    /**
     * Handle the Events "restored" event.
     */
    public function restored(Events $event): void
    {
        //
    }

    /**
     * Handle the Events "force deleted" event.
     */
    public function forceDeleted(Events $event): void
    {
        //
    }
}
