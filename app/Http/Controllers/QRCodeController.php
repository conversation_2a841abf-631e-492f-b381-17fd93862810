<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use App\Models\Events;

class QRCodeController extends Controller
{
    /**
     * Generate QR code untuk event
     */
    public function generateEventQR($eventId)
    {
        $event = Events::findOrFail($eventId);

        if (!$event->barcode_value) {
            abort(404, 'Event tidak memiliki barcode value');
        }

        // Generate QR code sebagai SVG
        $qrCode = QrCode::size(300)
            ->style('round')
            ->eye('circle')
            ->gradient(59, 130, 246, 16, 185, 129, 'diagonal')
            ->margin(2)
            ->generate($event->barcode_value);

        return response($qrCode)
            ->header('Content-Type', 'image/svg+xml');
    }

    /**
     * Halaman display QR code untuk event
     */
    public function showEventQR($eventId)
    {
        $event = Events::findOrFail($eventId);

        if (!$event->barcode_value) {
            abort(404, 'Event tidak memiliki barcode value');
        }

        return view('qrcode.event', compact('event'));
    }

    /**
     * Generate QR code untuk barcode value tertentu
     */
    public function generateQR(Request $request)
    {
        $barcodeValue = $request->get('value', 'EVENT-MEETING-001');

        // Generate QR code sebagai SVG
        $qrCode = QrCode::size(300)
            ->style('round')
            ->eye('circle')
            ->gradient(59, 130, 246, 16, 185, 129, 'diagonal')
            ->margin(2)
            ->generate($barcodeValue);

        return response($qrCode)
            ->header('Content-Type', 'image/svg+xml');
    }

    /**
     * Halaman demo QR code
     */
    public function demo()
    {
        $events = Events::where('is_active', true)->get();
        return view('qrcode.demo', compact('events'));
    }
}
