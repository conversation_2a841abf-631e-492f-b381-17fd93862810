<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Events;
use App\Models\EventParticipants;
use App\Models\Attendance;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AttendanceController extends Controller
{
    /**
     * Scan barcode dan dapatkan info event
     */
    public function scanBarcode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'barcode_value' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Barcode tidak valid',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $event = Events::findByBarcode($request->barcode_value);

            if (!$event) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event tidak ditemukan atau sudah tidak aktif'
                ], 404);
            }

            // Get available participants (yang belum attendance)
            $availableParticipants = $event->eventParticipants()
                ->with(['participant', 'group'])
                ->notAttended()
                ->get()
                ->map(function ($eventParticipant) {
                    return [
                        'id' => $eventParticipant->id,
                        'participant_id' => $eventParticipant->participant_id,
                        'name' => $eventParticipant->participant->fullname,
                        'company' => $eventParticipant->participant->company_name,
                        'formatted_name' => $eventParticipant->participant->formatted_name,
                        'group_id' => $eventParticipant->group_id,
                        'group_name' => $eventParticipant->group->name ?? 'No Group',
                    ];
                });

            return response()->json([
                'success' => true,
                'message' => 'Event ditemukan',
                'data' => [
                    'event' => [
                        'id' => $event->id,
                        'name' => $event->event_name,
                        'date' => $event->event_date->format('Y-m-d H:i:s'),
                        'location' => $event->event_location,
                        'description' => $event->description,
                    ],
                    'available_participants' => $availableParticipants
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan sistem',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Submit attendance participant
     */
    public function submitAttendance(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_participant_id' => 'required|exists:event_participants,id',
            'location' => 'nullable|string',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            DB::beginTransaction();

            $eventParticipant = EventParticipants::with(['participant', 'group', 'event'])
                ->find($request->event_participant_id);

            // Check apakah sudah attendance
            if ($eventParticipant->hasAttended()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Participant sudah melakukan attendance sebelumnya'
                ], 400);
            }

            // Create attendance record
            $attendance = Attendance::createAttendance(
                $eventParticipant->id,
                $request->only(['location', 'notes'])
            );

            // Get group info dan participants dalam group yang sama
            $groupParticipants = [];
            if ($eventParticipant->group_id) {
                $groupParticipants = EventParticipants::where('event_id', $eventParticipant->event_id)
                    ->where('group_id', $eventParticipant->group_id)
                    ->with(['participant', 'attendance'])
                    ->get()
                    ->map(function ($ep) {
                        return [
                            'name' => $ep->participant->fullname,
                            'company' => $ep->participant->company_name,
                            'has_attended' => $ep->hasAttended(),
                            'attendance_time' => $ep->attendance ? $ep->attendance->check_in_time->format('H:i:s') : null
                        ];
                    });
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Attendance berhasil dicatat',
                'data' => [
                    'participant' => [
                        'name' => $eventParticipant->participant->fullname,
                        'company' => $eventParticipant->participant->company_name,
                    ],
                    'event' => [
                        'name' => $eventParticipant->event->event_name,
                        'date' => $eventParticipant->event->event_date->format('Y-m-d'),
                        'location' => $eventParticipant->event->event_location,
                    ],
                    'group' => [
                        'id' => $eventParticipant->group_id,
                        'name' => $eventParticipant->group->name ?? 'No Group',
                        'participants' => $groupParticipants
                    ],
                    'attendance' => [
                        'check_in_time' => $attendance->check_in_time->format('Y-m-d H:i:s'),
                        'location' => $attendance->location,
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan attendance',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get group info dengan participants
     */
    public function getGroupInfo(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|exists:events,id',
            'group_id' => 'required|exists:groups,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Parameter tidak valid',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $event = Events::find($request->event_id);
            $groupParticipants = EventParticipants::where('event_id', $request->event_id)
                ->where('group_id', $request->group_id)
                ->with(['participant', 'group', 'attendance'])
                ->get();

            $participants = $groupParticipants->map(function ($ep) {
                return [
                    'name' => $ep->participant->fullname,
                    'company' => $ep->participant->company_name,
                    'has_attended' => $ep->hasAttended(),
                    'attendance_time' => $ep->attendance ? $ep->attendance->check_in_time->format('H:i:s') : null
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'event_name' => $event->event_name,
                    'group_name' => $groupParticipants->first()->group->name ?? 'No Group',
                    'total_participants' => $participants->count(),
                    'attended_count' => $participants->where('has_attended', true)->count(),
                    'participants' => $participants
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan sistem',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
