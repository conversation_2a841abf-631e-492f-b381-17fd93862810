<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class AttendanceWebController extends Controller
{
    /**
     * Halaman utama barcode scanner
     */
    public function index()
    {
        return view('attendance.scanner');
    }

    /**
     * Halaman pilih participant setelah scan barcode
     */
    public function selectParticipant(Request $request)
    {
        $barcodeValue = $request->get('barcode');
        return view('attendance.select-participant', compact('barcodeValue'));
    }

    /**
     * Halaman konfirmasi setelah submit attendance
     * Menampilkan informasi grup secara otomatis
     */
    public function confirmation(Request $request)
    {
        $eventParticipantId = $request->get('event_participant_id');

        // Validasi event participant ID
        if (!$eventParticipantId) {
            return redirect()->route('attendance.scanner')
                ->with('error', 'ID peserta tidak ditemukan');
        }

        return view('attendance.confirmation', compact('eventParticipantId'));
    }
}
