<?php

namespace App\Filament\Resources;

use App\Filament\Resources\GroupsResource\Pages;
use App\Filament\Resources\GroupsResource\RelationManagers;
use App\Models\Groups;
use Filament\Forms;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GroupsResource extends Resource
{
    protected static ?string $model = Groups::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = "Grup";
    protected static ?string $modelLabel = "Grup";
    protected static ?string $navigationGroup = "Managements";
    protected static ?int $navigationSort = 1;
    protected static ?string $recordTitleAttribute = "name";

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Grup')
                    ->schema([
                        TextInput::make('name')
                            ->label('Nama Grup')
                            ->required()
                            ->maxLength(255),
                        Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3),
                        TextInput::make('max_capacity')
                            ->label('Kapasitas Maksimal')
                            ->numeric()
                            ->minValue(1)
                            ->helperText('Jumlah maksimal anggota dalam grup ini'),
                        TextInput::make('color_code')
                            ->label('Kode Warna')
                            ->maxLength(7)
                            ->placeholder('#FF5733')
                            ->helperText('Kode warna hex untuk identifikasi grup (contoh: #FF5733)'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Grup')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->searchable(),
                TextColumn::make('max_capacity')
                    ->label('Kapasitas Maksimal')
                    ->sortable(),
                TextColumn::make('color_code')
                    ->label('Warna')
                    ->badge()
                    ->color(fn ($state) => $state ? 'primary' : 'gray'),
                TextColumn::make('eventParticipants_count')
                    ->label('Total Anggota')
                    ->counts('eventParticipants')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGroups::route('/'),
            'create' => Pages\CreateGroups::route('/create'),
            'edit' => Pages\EditGroups::route('/{record}/edit'),
        ];
    }
}
