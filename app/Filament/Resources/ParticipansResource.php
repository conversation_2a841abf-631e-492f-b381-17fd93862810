<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ParticipansResource\Pages;
use App\Filament\Resources\ParticipansResource\RelationManagers;
use App\Models\Participants;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ParticipansResource extends Resource
{
    protected static ?string $model = Participants::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = "Participants";
    protected static ?string $modelLabel = "Participants";
    protected static ?string $navigationGroup = "Managements ";
    protected static ?int $navigationSort = 2;
    protected static ?string $recordTitleAttribute = "name";

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Participans')
                        ->schema([
                            TextInput::make('full_name')
                                    ->required(),
                            TextInput::make('email')
                                    ->email()
                                    ->required(),
                            TextInput::make('phone')
                                    ->numeric()
                                    ->required(),
                            TextInput::make('position')
                                    ->required(),
                            TextInput::make('company_name')
                                    ->required(),
                        ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('full_name')
                        ->sortable()
                        ->searchable(),
                TextColumn::make('email')
                        ->sortable()
                        ->searchable(),
                TextColumn::make('phone')
                        ->sortable()
                        ->searchable(),
                TextColumn::make('position')
                        ->sortable()
                        ->searchable(),
                TextColumn::make('company_name')
                        ->sortable()
                        ->searchable(),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListParticipans::route('/'),
            'create' => Pages\CreateParticipans::route('/create'),
            'edit' => Pages\EditParticipans::route('/{record}/edit'),
        ];
    }
}
