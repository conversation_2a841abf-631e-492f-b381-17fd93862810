<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ParticipansResource\Pages;
use App\Filament\Resources\ParticipansResource\RelationManagers;
use App\Models\Participants;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ParticipansResource extends Resource
{
    protected static ?string $model = Participants::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = "Peserta";
    protected static ?string $modelLabel = "Peserta";
    protected static ?string $navigationGroup = "Managements ";
    protected static ?int $navigationSort = 2;
    protected static ?string $recordTitleAttribute = "fullname";

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Peserta')
                        ->schema([
                            TextInput::make('fullname')
                                    ->label('Nama Lengkap')
                                    ->required(),
                            TextInput::make('email')
                                    ->label('Email')
                                    ->email()
                                    ->required(),
                            TextInput::make('phone')
                                    ->label('Telepon')
                                    ->tel()
                                    ->required(),
                            TextInput::make('position')
                                    ->label('Posisi/Jabatan')
                                    ->required(),
                            TextInput::make('company_name')
                                    ->label('Nama Perusahaan')
                                    ->required(),
                        ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('fullname')
                        ->label('Nama Lengkap')
                        ->sortable()
                        ->searchable(),
                TextColumn::make('email')
                        ->label('Email')
                        ->sortable()
                        ->searchable(),
                TextColumn::make('phone')
                        ->label('Telepon')
                        ->sortable()
                        ->searchable(),
                TextColumn::make('position')
                        ->label('Posisi')
                        ->sortable()
                        ->searchable(),
                TextColumn::make('company_name')
                        ->label('Perusahaan')
                        ->sortable()
                        ->searchable(),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListParticipans::route('/'),
            'create' => Pages\CreateParticipans::route('/create'),
            'edit' => Pages\EditParticipans::route('/{record}/edit'),
        ];
    }
}
