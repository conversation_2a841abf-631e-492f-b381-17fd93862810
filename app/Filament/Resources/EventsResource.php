<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EventsResource\Pages;
use App\Filament\Resources\EventsResource\RelationManagers;
use App\Models\Events;
use DateTime;
use Filament\Forms;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;

class EventsResource extends Resource
{
    protected static ?string $model = Events::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = "Events";
    protected static ?string $modelLabel = "Events";
    protected static ?string $navigationGroup = "Managements";
    protected static ?int $navigationSort = 1;
    protected static ?string $recordTitleAttribute = "name";

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Event Information')
                    ->schema([
                        TextInput::make('event_name')
                            ->label('Event Name')
                            ->required()
                            ->maxLength(255),
                        DateTimePicker::make('event_date')
                            ->label('Event Date & Time')
                            ->required(),
                        Textarea::make('description')
                            ->label('Description')
                            ->required()
                            ->rows(3),
                        TextInput::make('event_location')
                            ->label('Location')
                            ->required()
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Section::make('Barcode Settings')
                    ->schema([
                        TextInput::make('barcode_value')
                            ->label('Barcode Value')
                            ->unique(ignoreRecord: true)
                            ->helperText('Leave empty to auto-generate')
                            ->afterStateUpdated(function ($state, $set) {
                                if (empty($state)) {
                                    $set('barcode_value', 'EVENT-' . strtoupper(Str::random(8)));
                                }
                            }),
                        Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->helperText('Only active events can be scanned'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('event_name')
                    ->label('Event Name')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('event_date')
                    ->label('Date & Time')
                    ->dateTime('d M Y, H:i')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('event_location')
                    ->label('Location')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('barcode_value')
                    ->label('Barcode')
                    ->copyable()
                    ->copyMessage('Barcode copied!')
                    ->searchable(),
                BadgeColumn::make('is_active')
                    ->label('Status')
                    ->colors([
                        'success' => true,
                        'danger' => false,
                    ])
                    ->formatStateUsing(fn ($state) => $state ? 'Active' : 'Inactive'),
                TextColumn::make('eventParticipants_count')
                    ->label('Participants')
                    ->counts('eventParticipants')
                    ->sortable(),
                TextColumn::make('attendances_count')
                    ->label('Attended')
                    ->counts('attendances')
                    ->sortable(),
                ImageColumn::make('qr_code')
                    ->label('QR Code')
                    ->getStateUsing(fn ($record) => route('qrcode.generate.event', $record->id))
                    ->size(60)
                    ->square(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('view_qr')
                    ->label('View QR')
                    ->icon('heroicon-o-qr-code')
                    ->url(fn ($record) => route('qrcode.event', $record->id))
                    ->openUrlInNewTab(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEvents::route('/'),
            'create' => Pages\CreateEvents::route('/create'),
            'edit' => Pages\EditEvents::route('/{record}/edit'),
        ];
    }
}
