<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AttendanceResource\Pages;
use App\Filament\Resources\AttendanceResource\RelationManagers;
use App\Models\Attendance;
use App\Models\Events;
use App\Models\EventParticipants;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AttendanceResource extends Resource
{
    protected static ?string $model = Attendance::class;

    protected static ?string $navigationIcon = 'heroicon-o-check-circle';
    protected static ?string $navigationLabel = 'Attendance Records';
    protected static ?string $modelLabel = 'Attendance';
    protected static ?string $navigationGroup = 'Reports';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('event_participant_id')
                    ->label('Event Participant')
                    ->relationship('eventParticipant')
                    ->getOptionLabelFromRecordUsing(function ($record) {
                        return $record->participant->full_name . ' - ' . $record->event->event_name;
                    })
                    ->searchable()
                    ->preload()
                    ->required(),
                DateTimePicker::make('check_in_time')
                    ->label('Check-in Time')
                    ->default(now())
                    ->required(),
                TextInput::make('location')
                    ->label('Location')
                    ->maxLength(255),
                TextInput::make('device_ip')
                    ->label('Device IP')
                    ->maxLength(45),
                TextInput::make('user_agent')
                    ->label('User Agent')
                    ->maxLength(500),
                Textarea::make('notes')
                    ->label('Notes')
                    ->rows(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('eventParticipant.participant.full_name')
                    ->label('Participant')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('eventParticipant.event.event_name')
                    ->label('Event')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('eventParticipant.group.name')
                    ->label('Group')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('check_in_time')
                    ->label('Check-in Time')
                    ->dateTime('d M Y, H:i:s')
                    ->sortable(),
                TextColumn::make('location')
                    ->label('Location')
                    ->searchable(),
                TextColumn::make('device_ip')
                    ->label('Device IP')
                    ->searchable(),
                TextColumn::make('created_at')
                    ->label('Recorded At')
                    ->dateTime('d M Y, H:i:s')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('event')
                    ->relationship('eventParticipant.event', 'event_name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('group')
                    ->relationship('eventParticipant.group', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('check_in_time', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAttendances::route('/'),
            'create' => Pages\CreateAttendance::route('/create'),
            'edit' => Pages\EditAttendance::route('/{record}/edit'),
        ];
    }
}
