<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EventParticipantGroupResource\Pages;
use App\Models\EventParticipants;
use App\Models\Events;
use App\Models\Participants;
use App\Models\Groups;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;

class EventParticipantGroupResource extends Resource
{
    protected static ?string $model = EventParticipants::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationLabel = "Manajemen Grup Peserta";
    protected static ?string $modelLabel = "Grup Peserta";
    protected static ?string $navigationGroup = "Managements";
    protected static ?int $navigationSort = 3;
    protected static ?string $recordTitleAttribute = "participant.fullname";

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Pilih Event')
                    ->schema([
                        Select::make('event_id')
                            ->label('Event')
                            ->relationship('event', 'event_name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn ($state, $set) => $set('participant_id', null)),
                    ])
                    ->columns(1),

                Section::make('Pengaturan Grup')
                    ->schema([
                        Select::make('group_id')
                            ->label('Grup')
                            ->relationship('group', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->createOptionForm([
                                TextInput::make('name')
                                    ->label('Nama Grup')
                                    ->required(),
                                TextInput::make('max_capacity')
                                    ->label('Kapasitas Maksimal')
                                    ->numeric()
                                    ->minValue(1),
                            ]),
                        Select::make('participant_id')
                            ->label('Peserta')
                            ->relationship('participant', 'fullname')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->getOptionLabelFromRecordUsing(fn ($record) => $record->fullname . ' - ' . $record->company_name),
                    ])
                    ->columns(2),

                Section::make('Status Pendaftaran')
                    ->schema([
                        Select::make('registration_status')
                            ->label('Status Pendaftaran')
                            ->options([
                                'pending' => 'Menunggu',
                                'confirmed' => 'Dikonfirmasi',
                                'cancelled' => 'Dibatalkan',
                            ])
                            ->default('pending')
                            ->required(),
                        TextInput::make('assigned_table')
                            ->label('Meja yang Ditugaskan')
                            ->maxLength(255),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('event.event_name')
                    ->label('Event')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('participant.fullname')
                    ->label('Nama Peserta')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('participant.company_name')
                    ->label('Perusahaan')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('group.name')
                    ->label('Grup')
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->color('primary'),
                BadgeColumn::make('registration_status')
                    ->label('Status')
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'confirmed',
                        'danger' => 'cancelled',
                    ])
                    ->formatStateUsing(fn ($state) => match($state) {
                        'pending' => 'Menunggu',
                        'confirmed' => 'Dikonfirmasi',
                        'cancelled' => 'Dibatalkan',
                        default => $state,
                    }),
                TextColumn::make('assigned_table')
                    ->label('Meja')
                    ->sortable(),
                BadgeColumn::make('attendance_status')
                    ->label('Kehadiran')
                    ->getStateUsing(fn ($record) => $record->attendance ? 'Hadir' : 'Belum Hadir')
                    ->colors([
                        'success' => 'Hadir',
                        'gray' => 'Belum Hadir',
                    ]),
            ])
            ->filters([
                SelectFilter::make('event')
                    ->relationship('event', 'event_name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('group')
                    ->relationship('group', 'name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('registration_status')
                    ->label('Status Pendaftaran')
                    ->options([
                        'pending' => 'Menunggu',
                        'confirmed' => 'Dikonfirmasi',
                        'cancelled' => 'Dibatalkan',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEventParticipantGroups::route('/'),
            'create' => Pages\CreateEventParticipantGroup::route('/create'),
            'edit' => Pages\EditEventParticipantGroup::route('/{record}/edit'),
        ];
    }
}
