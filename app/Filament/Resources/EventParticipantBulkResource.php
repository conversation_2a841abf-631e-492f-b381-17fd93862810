<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EventParticipantBulkResource\Pages;
use App\Models\Events;
use App\Models\Participants;
use App\Models\Groups;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class EventParticipantBulkResource extends Resource
{
    protected static ?string $model = null; // Tidak menggunakan model spesifik

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = "Tambah Peserta Massal";
    protected static ?string $modelLabel = "Tambah Peserta Massal";
    protected static ?string $navigationGroup = "Managements";
    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Pilih Event dan Grup')
                    ->schema([
                        Select::make('event_id')
                            ->label('Event')
                            ->options(Events::where('is_active', true)->pluck('event_name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn ($state, $set) => $set('participant_ids', [])),
                        Select::make('group_id')
                            ->label('Grup')
                            ->relationship('group', 'name', fn ($query) => $query->orderBy('name'))
                            ->searchable()
                            ->preload()
                            ->required()
                            ->createOptionForm([
                                TextInput::make('name')
                                    ->label('Nama Grup')
                                    ->required(),
                                Textarea::make('description')
                                    ->label('Deskripsi')
                                    ->rows(2),
                                TextInput::make('max_capacity')
                                    ->label('Kapasitas Maksimal')
                                    ->numeric()
                                    ->minValue(1),
                                TextInput::make('color_code')
                                    ->label('Kode Warna')
                                    ->placeholder('#FF5733'),
                            ]),
                    ])
                    ->columns(2),

                Section::make('Pilih Peserta')
                    ->schema([
                        Select::make('participant_ids')
                            ->label('Peserta')
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->required()
                            ->options(function (callable $get) {
                                $eventId = $get('event_id');
                                if (!$eventId) {
                                    return Participants::all()->pluck('fullname_with_company', 'id');
                                }
                                
                                // Ambil participants yang belum terdaftar di event ini
                                return Participants::whereDoesntHave('eventParticipants', function ($query) use ($eventId) {
                                    $query->where('event_id', $eventId);
                                })
                                ->get()
                                ->pluck('fullname_with_company', 'id');
                            })
                            ->getSearchResultsUsing(function (string $search) {
                                return Participants::where('fullname', 'like', "%{$search}%")
                                    ->orWhere('company_name', 'like', "%{$search}%")
                                    ->limit(50)
                                    ->get()
                                    ->pluck('fullname_with_company', 'id');
                            })
                            ->helperText('Pilih satu atau lebih peserta untuk ditambahkan ke grup')
                            ->columnSpanFull(),
                    ]),

                Section::make('Pengaturan Pendaftaran')
                    ->schema([
                        Select::make('registration_status')
                            ->label('Status Pendaftaran')
                            ->options([
                                'pending' => 'Menunggu',
                                'confirmed' => 'Dikonfirmasi',
                                'cancelled' => 'Dibatalkan',
                            ])
                            ->default('confirmed')
                            ->required(),
                        TextInput::make('assigned_table')
                            ->label('Meja yang Ditugaskan')
                            ->maxLength(255)
                            ->helperText('Opsional - akan diterapkan untuk semua peserta'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Tidak ada table karena ini resource untuk bulk create saja
            ])
            ->filters([
                //
            ])
            ->actions([
                //
            ])
            ->bulkActions([
                //
            ]);
    }

    public static function getPages(): array
    {
        return [
            'create' => Pages\CreateEventParticipantBulk::route('/create'),
        ];
    }

    public static function canViewAny(): bool
    {
        return true;
    }

    public static function getNavigationBadge(): ?string
    {
        return null;
    }
}
