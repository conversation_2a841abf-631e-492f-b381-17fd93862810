<?php

namespace App\Filament\Resources\EventParticipantGroupResource\Pages;

use App\Filament\Resources\EventParticipantGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListEventParticipantGroups extends ListRecords
{
    protected static string $resource = EventParticipantGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Tambah Peserta ke Grup'),
        ];
    }
}
