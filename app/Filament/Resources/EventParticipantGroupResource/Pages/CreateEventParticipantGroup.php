<?php

namespace App\Filament\Resources\EventParticipantGroupResource\Pages;

use App\Filament\Resources\EventParticipantGroupResource;
use Filament\Resources\Pages\CreateRecord;

class CreateEventParticipantGroup extends CreateRecord
{
    protected static string $resource = EventParticipantGroupResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Peserta berhasil ditambahkan ke grup';
    }
}
