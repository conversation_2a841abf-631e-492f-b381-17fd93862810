<?php

namespace App\Filament\Resources\EventParticipantGroupResource\Pages;

use App\Filament\Resources\EventParticipantGroupResource;
use App\Models\EventParticipants;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateEventParticipantGroup extends CreateRecord
{
    protected static string $resource = EventParticipantGroupResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // Ambil participant_ids dan hapus dari data
        $participantIds = $data['participant_ids'] ?? [];
        unset($data['participant_ids']);

        // Buat record untuk setiap participant
        $createdRecords = [];
        foreach ($participantIds as $participantId) {
            $recordData = array_merge($data, ['participant_id' => $participantId]);
            $createdRecords[] = EventParticipants::create($recordData);
        }

        // Return record pertama (untuk compatibility dengan Filament)
        return $createdRecords[0] ?? new EventParticipants();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        $participantCount = count($this->data['participant_ids'] ?? []);
        return "Berhasil menambahkan {$participantCount} peserta ke grup";
    }
}
