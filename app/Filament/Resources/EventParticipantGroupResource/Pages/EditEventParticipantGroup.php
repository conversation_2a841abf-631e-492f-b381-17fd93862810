<?php

namespace App\Filament\Resources\EventParticipantGroupResource\Pages;

use App\Filament\Resources\EventParticipantGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditEventParticipantGroup extends EditRecord
{
    protected static string $resource = EventParticipantGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Data grup peserta berhasil diperbarui';
    }
}
