<?php

namespace App\Filament\Resources\EventParticipantBulkResource\Pages;

use App\Filament\Resources\EventParticipantBulkResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListEventParticipantBulks extends ListRecords
{
    protected static string $resource = EventParticipantBulkResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Tambah Peserta Massal'),
        ];
    }

    public function getTitle(): string
    {
        return 'Manajemen Peserta';
    }
}
