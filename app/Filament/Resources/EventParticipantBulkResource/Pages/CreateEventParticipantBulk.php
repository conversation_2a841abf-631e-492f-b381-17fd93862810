<?php

namespace App\Filament\Resources\EventParticipantBulkResource\Pages;

use App\Filament\Resources\EventParticipantBulkResource;
use App\Models\EventParticipants;
use App\Models\Groups;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;

class CreateEventParticipantBulk extends CreateRecord
{
    protected static string $resource = EventParticipantBulkResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // Ambil data yang diperlukan
        $eventId = $data['event_id'];
        $groupId = $data['group_id'];
        $participantIds = $data['participant_ids'] ?? [];
        $registrationStatus = $data['registration_status'] ?? 'confirmed';
        $assignedTable = $data['assigned_table'] ?? null;

        // Validasi kapasitas grup jika ada
        $group = Groups::find($groupId);
        if ($group && $group->max_capacity) {
            $currentCount = EventParticipants::where('event_id', $eventId)
                ->where('group_id', $groupId)
                ->count();

            $newTotal = $currentCount + count($participantIds);

            if ($newTotal > $group->max_capacity) {
                Notification::make()
                    ->title('Kapasitas Grup Terlampaui')
                    ->body("Grup '{$group->name}' memiliki kapasitas maksimal {$group->max_capacity}. Saat ini ada {$currentCount} anggota, tidak bisa menambah {count($participantIds)} peserta lagi.")
                    ->danger()
                    ->send();

                // Return dummy model untuk mencegah error
                return new EventParticipants();
            }
        }

        // Buat record untuk setiap participant
        $createdRecords = [];
        $successCount = 0;
        $duplicateCount = 0;

        foreach ($participantIds as $participantId) {
            // Cek apakah participant sudah terdaftar di event ini
            $existing = EventParticipants::where('event_id', $eventId)
                ->where('participant_id', $participantId)
                ->first();

            if ($existing) {
                $duplicateCount++;
                continue;
            }

            $recordData = [
                'event_id' => $eventId,
                'group_id' => $groupId,
                'participant_id' => $participantId,
                'registration_status' => $registrationStatus,
                'assigned_table' => $assignedTable,
            ];

            $createdRecords[] = EventParticipants::create($recordData);
            $successCount++;
        }

        // Kirim notifikasi hasil
        $message = "Berhasil menambahkan {$successCount} peserta ke grup";
        if ($duplicateCount > 0) {
            $message .= ". {$duplicateCount} peserta sudah terdaftar sebelumnya";
        }

        Notification::make()
            ->title('Peserta Berhasil Ditambahkan')
            ->body($message)
            ->success()
            ->send();

        // Return record pertama atau dummy model
        return $createdRecords[0] ?? new EventParticipants();
    }

    protected function getRedirectUrl(): string
    {
        return '/admin/event-participant-groups';
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return null; // Kita sudah handle notifikasi di handleRecordCreation
    }

    public function getTitle(): string
    {
        return 'Tambah Peserta Massal ke Grup';
    }
}
